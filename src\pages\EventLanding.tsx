import { useEffect, useRef, useState } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import {
  Clock,
  Users,
  Star,
  CheckCircle,
  ArrowRight,
  Calendar,
  MapPin,
  Trophy,
  Zap,
  Shield
} from 'lucide-react'
import WhatsAppFloat from '@/components/WhatsAppFloat'
import RegistrationModal from '@/components/RegistrationModal'

gsap.registerPlugin(ScrollTrigger)

const EventLanding = () => {
  const [timeLeft, setTimeLeft] = useState({
    days: 7,
    hours: 12,
    minutes: 45,
    seconds: 30
  })
  const [isModalOpen, setIsModalOpen] = useState(false)

  const heroRef = useRef<HTMLDivElement>(null)
  const ctaRef = useRef<HTMLDivElement>(null)

  const openRegistrationModal = () => {
    setIsModalOpen(true)
  }

  const closeRegistrationModal = () => {
    setIsModalOpen(false)
  }

  // Countdown timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev.seconds > 0) {
          return { ...prev, seconds: prev.seconds - 1 }
        } else if (prev.minutes > 0) {
          return { ...prev, minutes: prev.minutes - 1, seconds: 59 }
        } else if (prev.hours > 0) {
          return { ...prev, hours: prev.hours - 1, minutes: 59, seconds: 59 }
        } else if (prev.days > 0) {
          return {
            ...prev,
            days: prev.days - 1,
            hours: 23,
            minutes: 59,
            seconds: 59
          }
        }
        return prev
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // GSAP Animations
  useEffect(() => {
    const tl = gsap.timeline()

    tl.fromTo(
      heroRef.current?.children,
      { y: 50, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.8, stagger: 0.2, ease: 'power3.out' }
    )

    // Floating CTA animation
    gsap.to(ctaRef.current, {
      y: -10,
      duration: 2,
      repeat: -1,
      yoyo: true,
      ease: 'power2.inOut'
    })

    return () => {
      tl.kill()
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill())
    }
  }, [])

  const benefits = [
    {
      icon: Trophy,
      title: 'Master Coding with AI',
      description:
        'Learn to integrate powerful AI tools for smarter, faster development'
    },
    {
      icon: Zap,
      title: 'Build Real-World AI Apps',
      description:
        'Create intelligent applications with hands-on practical experience'
    },
    {
      icon: Users,
      title: 'Master AI Tools & Libraries',
      description: 'Get proficient with cutting-edge AI development frameworks'
    },
    {
      icon: Shield,
      title: '10X Developer Skills',
      description:
        'Become a developer with in-demand skills for future-proof tech jobs'
    }
  ]

  const testimonials = [
    {
      name: 'David Okafor',
      role: 'Software Developer',
      content:
        'This workshop completely changed how I approach coding. The AI tools I learned increased my productivity by 300%!',
      rating: 5
    },
    {
      name: 'Amina Hassan',
      role: 'Frontend Developer',
      content:
        'The hands-on approach was incredible. I built my first AI-powered app during the workshop and landed a job the next week!',
      rating: 5
    },
    {
      name: 'Chidi Okwu',
      role: 'Full-Stack Developer',
      content:
        'Best investment in my coding career. The AI integration techniques are game-changers for modern development.',
      rating: 5
    }
  ]

  return (
    <div className="bg-agency-dark min-h-screen text-white overflow-x-hidden">
      {/* Minimal Landing Page Header - Conversion Focused */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-agency-dark/95 backdrop-blur-sm border-b border-agency-green/10">
        <div className="container mx-auto px-4 py-3 flex justify-between items-center">
          {/* Logo */}
          <div className="flex items-center">
            <img src="/logo-01.svg" alt="KavaraDigital" className="w-40 h-10" />
          </div>

          {/* Trust Indicators & Contact */}
          <div className="hidden md:flex items-center gap-6 text-sm">
            <div className="flex items-center gap-2 text-agency-white-muted">
              <Shield size={16} className="text-agency-green" />
              <span>Secure Registration</span>
            </div>
            <div className="text-agency-white-muted">
              Questions?{' '}
              <a
                href="tel:+2348029020121"
                className="text-agency-green hover:underline font-medium"
              >
                0802-902-0121
              </a>
            </div>
          </div>

          {/* Mobile Contact */}
          <div className="md:hidden">
            <a
              href="tel:+2348029020121"
              className="text-agency-green hover:underline text-sm font-medium"
            >
              Call Now
            </a>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center px-4 pt-24">
        <div className="absolute inset-0 bg-gradient-overlay"></div>

        <div
          ref={heroRef}
          className="relative z-10 max-w-4xl mx-auto text-center"
        >
          {/* Urgency Banner */}
          <div className="inline-flex items-center gap-2 bg-red-600 text-white px-4 py-2 rounded-full text-sm font-medium mb-6">
            <Clock size={16} />
            <span>Limited Seats: Physical Workshop - Register Now!</span>
          </div>

          {/* Main Headline */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
            AI & CODE
            <span className="text-gradient block">WORKSHOP</span>
          </h1>

          {/* Subheadline */}
          <p className="text-xl md:text-2xl text-agency-white-muted mb-8 max-w-3xl mx-auto">
            Master your code with our comprehensive, all-hands-on physical
            workshop. Integrate powerful AI tools for smarter, faster
            development.
          </p>

          {/* Event Details */}
          <div className="flex flex-wrap justify-center gap-6 mb-8 text-agency-white-muted">
            <div className="flex items-center gap-2">
              <Calendar size={20} className="text-agency-green" />
              <span>August 23rd, 2024</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock size={20} className="text-agency-green" />
              <span>09:00 AM - 04:00 PM</span>
            </div>
            <div className="flex items-center gap-2">
              <MapPin size={20} className="text-agency-green" />
              <span>Physical Workshop + Lodging Available</span>
            </div>
          </div>

          {/* Countdown Timer */}
          <div className="grid grid-cols-4 gap-4 max-w-md mx-auto mb-8">
            {Object.entries(timeLeft).map(([unit, value]) => (
              <div
                key={unit}
                className="bg-agency-darker rounded-lg p-4 border border-agency-green/20"
              >
                <div className="text-2xl md:text-3xl font-bold text-agency-green">
                  {value}
                </div>
                <div className="text-sm text-agency-white-muted capitalize">
                  {unit}
                </div>
              </div>
            ))}
          </div>

          {/* Primary CTA */}
          <div ref={ctaRef} className="space-y-4">
            <button
              onClick={openRegistrationModal}
              className="bg-agency-green text-agency-dark px-8 py-4 rounded-lg font-bold text-lg hover:bg-opacity-90 transition-all btn-glow inline-flex items-center gap-2"
            >
              Secure Your Spot Now
              <ArrowRight size={20} />
            </button>
            <p className="text-sm text-agency-white-muted">
              ⚡ Only 47 spots remaining • 💰 Early Bird: ₦50,000 (Regular:
              ₦75,000)
            </p>
          </div>
        </div>
      </section>

      {/* Social Proof Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Join Industry Leaders Who've Already Transformed Their Business
            </h2>
            <div className="flex justify-center items-center gap-2 text-agency-green">
              {[...Array(5)].map((_, i) => (
                <Star key={i} size={24} fill="currentColor" />
              ))}
              <span className="ml-2 text-white">
                4.9/5 from 1,200+ attendees
              </span>
            </div>
          </div>

          {/* Testimonials Grid */}
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className="bg-agency-darker p-6 rounded-xl border border-agency-green/10"
              >
                <div className="flex gap-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star
                      key={i}
                      size={16}
                      fill="currentColor"
                      className="text-agency-green"
                    />
                  ))}
                </div>
                <p className="text-agency-white-muted mb-4">
                  "{testimonial.content}"
                </p>
                <div>
                  <div className="font-semibold">{testimonial.name}</div>
                  <div className="text-sm text-agency-white-muted">
                    {testimonial.role}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 px-4 bg-agency-darker">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              What You'll Master
            </h2>
            <p className="text-xl text-agency-white-muted">
              This isn't just another workshop. It's your complete AI-powered
              coding transformation.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-agency-green/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <benefit.icon size={32} className="text-agency-green" />
                </div>
                <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
                <p className="text-agency-white-muted">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* What You'll Learn Section */}
      <section className="py-16 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Comprehensive AI & Code Curriculum
            </h2>
            <p className="text-xl text-agency-white-muted">
              No fluff. No theory. Just hands-on AI coding techniques you can
              use immediately.
            </p>
          </div>

          <div className="space-y-6">
            {[
              'AI-powered code generation and automation techniques',
              'Building intelligent applications with machine learning integration',
              'Mastering GitHub Copilot, ChatGPT, and other AI coding assistants',
              'Creating smart debugging and testing workflows with AI',
              'Developing AI-enhanced web and mobile applications',
              'Future-proofing your career with in-demand AI development skills'
            ].map((item, index) => (
              <div key={index} className="flex items-start gap-4">
                <CheckCircle
                  size={24}
                  className="text-agency-green flex-shrink-0 mt-1"
                />
                <p className="text-lg">{item}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Urgency Section */}
      <section className="py-16 px-4 bg-gradient-to-r from-red-900/20 to-agency-darker">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-red-400">
            ⚠️ This Opportunity Won't Last Long
          </h2>
          <p className="text-xl text-agency-white-muted mb-8">
            We're limiting attendance to ensure quality networking and
            personalized attention. Once we hit 500 attendees, registration
            closes permanently.
          </p>

          <div className="bg-agency-darker p-6 rounded-xl border border-red-500/20 mb-8">
            <div className="text-2xl font-bold text-red-400 mb-2">
              Only 47 Spots Remaining
            </div>
            <div className="w-full bg-gray-700 rounded-full h-4 mb-4">
              <div
                className="bg-red-500 h-4 rounded-full"
                style={{ width: '91%' }}
              ></div>
            </div>
            <p className="text-agency-white-muted">
              453 entrepreneurs have already secured their spot
            </p>
          </div>

          <button
            onClick={openRegistrationModal}
            className="bg-agency-green text-agency-dark px-8 py-4 rounded-lg font-bold text-lg hover:bg-opacity-90 transition-all btn-glow inline-flex items-center gap-2 mb-4"
          >
            Reserve My Spot Before It's Gone
            <ArrowRight size={20} />
          </button>
          <p className="text-sm text-agency-white-muted">
            💳 Secure payment • 🔒 SSL encrypted • 💰 Money-back guarantee
          </p>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-16 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Investment Options
            </h2>
            <p className="text-xl text-agency-white-muted">
              Choose the package that fits your business goals
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Standard Ticket */}
            <div className="bg-agency-darker p-8 rounded-xl border border-agency-green/10">
              <h3 className="text-2xl font-bold mb-4">Standard Access</h3>
              <div className="mb-6">
                <span className="text-4xl font-bold text-agency-green">
                  ₦50,000
                </span>
                <span className="text-lg text-agency-white-muted line-through ml-2">
                  ₦75,000
                </span>
                <div className="text-sm text-red-400">
                  Early Bird Special - Save ₦25,000
                </div>
              </div>
              <ul className="space-y-3 mb-8">
                <li className="flex items-center gap-2">
                  <CheckCircle size={16} className="text-agency-green" />
                  <span>Full day access to all sessions</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle size={16} className="text-agency-green" />
                  <span>Networking lunch & refreshments</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle size={16} className="text-agency-green" />
                  <span>Digital resource pack (₦15,000 value)</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle size={16} className="text-agency-green" />
                  <span>Certificate of completion</span>
                </li>
              </ul>
              <button
                onClick={openRegistrationModal}
                className="w-full bg-agency-green text-agency-dark py-3 rounded-lg font-bold hover:bg-opacity-90 transition-all"
              >
                Get Standard Access
              </button>
            </div>

            {/* VIP Ticket */}
            <div className="bg-gradient-to-b from-agency-green/10 to-agency-darker p-8 rounded-xl border-2 border-agency-green relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-agency-green text-agency-dark px-4 py-1 rounded-full text-sm font-bold">
                MOST POPULAR
              </div>
              <h3 className="text-2xl font-bold mb-4">VIP Experience</h3>
              <div className="mb-6">
                <span className="text-4xl font-bold text-agency-green">
                  ₦95,000
                </span>
                <span className="text-lg text-agency-white-muted line-through ml-2">
                  ₦150,000
                </span>
                <div className="text-sm text-red-400">
                  Early Bird Special - Save ₦55,000
                </div>
              </div>
              <ul className="space-y-3 mb-8">
                <li className="flex items-center gap-2">
                  <CheckCircle size={16} className="text-agency-green" />
                  <span>Everything in Standard +</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle size={16} className="text-agency-green" />
                  <span>VIP seating (front row access)</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle size={16} className="text-agency-green" />
                  <span>Exclusive 1-hour mastermind session</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle size={16} className="text-agency-green" />
                  <span>Private networking dinner</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle size={16} className="text-agency-green" />
                  <span>1-on-1 photo with speakers</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle size={16} className="text-agency-green" />
                  <span>Bonus: 30-min consultation call</span>
                </li>
              </ul>
              <button
                onClick={openRegistrationModal}
                className="w-full bg-agency-green text-agency-dark py-3 rounded-lg font-bold hover:bg-opacity-90 transition-all"
              >
                Get VIP Experience
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-16 px-4 bg-agency-darker">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Don't Let This Opportunity Slip Away
          </h2>
          <p className="text-xl text-agency-white-muted mb-8">
            In 12 months, you'll either wish you had attended this event, or
            you'll be thanking yourself for making the decision that changed
            everything.
          </p>

          <div className="bg-agency-dark p-6 rounded-xl border border-agency-green/20 mb-8">
            <h3 className="text-xl font-bold mb-4 text-agency-green">
              100% Risk-Free Guarantee
            </h3>
            <p className="text-agency-white-muted">
              If you don't get at least 10x value from this event, we'll refund
              every penny. No questions asked. That's how confident we are in
              the content.
            </p>
          </div>

          <button
            onClick={openRegistrationModal}
            className="bg-agency-green text-agency-dark px-8 py-4 rounded-lg font-bold text-xl hover:bg-opacity-90 transition-all btn-glow inline-flex items-center gap-2 mb-4"
          >
            Claim Your Spot Now - Limited Time
            <ArrowRight size={24} />
          </button>

          <p className="text-sm text-agency-white-muted mb-8">
            ⏰ Early Bird pricing ends in {timeLeft.days} days, {timeLeft.hours}{' '}
            hours
          </p>

          <div className="text-center text-agency-white-muted">
            <p>
              Questions? Email us at{' '}
              <a
                href="mailto:<EMAIL>"
                className="text-agency-green hover:underline"
              >
                <EMAIL>
              </a>
            </p>
            <p>
              or call{' '}
              <a
                href="tel:+2348029020121"
                className="text-agency-green hover:underline"
              >
                0802-902-0121
              </a>
            </p>
          </div>
        </div>
      </section>

      {/* WhatsApp Float Button */}
      <WhatsAppFloat
        phoneNumber="+2348029020121"
        message="Hi! I'm interested in the AI & Code Workshop. Can you help me secure my spot and answer some questions?"
      />

      {/* Registration Modal */}
      <RegistrationModal
        isOpen={isModalOpen}
        onClose={closeRegistrationModal}
      />
    </div>
  )
}

export default EventLanding
